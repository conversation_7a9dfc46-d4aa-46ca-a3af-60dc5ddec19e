'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Header } from '@/components/header';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/common/textarea';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { toast } from 'sonner';
import { Loader2, Brain, ChevronRight } from 'lucide-react';
import type {
  EvaluatePositionRequest,
  EvaluatePositionResponse,
  EvaluatePositionError,
} from '@/types/chess';

const formSchema = z.object({
  position: z
    .string()
    .min(10, 'Position description must be at least 10 characters')
    .max(2000, 'Position description must be less than 2000 characters'),
});

type FormData = z.infer<typeof formSchema>;

const EvaluateChessPositionPage = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [evaluation, setEvaluation] = useState<string | null>(null);

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      position: '',
    },
  });

  const onSubmit = async (data: FormData) => {
    setIsLoading(true);
    setEvaluation(null);

    try {
      const response = await fetch('/api/evaluate-position', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          position: data.position,
        } as EvaluatePositionRequest),
      });

      if (!response.ok) {
        const errorData: EvaluatePositionError = await response.json();
        throw new Error(errorData.error || 'Failed to evaluate position');
      }

      const result: EvaluatePositionResponse = await response.json();
      setEvaluation(result.evaluation);
      toast.success('Position evaluated successfully!');
    } catch (error) {
      console.error('Error evaluating position:', error);
      toast.error(
        error instanceof Error ? error.message : 'Failed to evaluate position',
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <Header />
      <main className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="space-y-8">
          {/* Header Section */}
          <div className="text-center space-y-4">
            <div className="flex items-center justify-center gap-2">
              <Brain className="h-8 w-8 text-primary" />
              <h1 className="text-3xl font-bold">Chess Position Evaluator</h1>
            </div>
            <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
              Get AI-powered analysis of chess positions. Describe a position in
              FEN notation or plain English, and receive detailed evaluation
              including tactical and strategic insights.
            </p>
          </div>

          {/* Form Section */}
          <div className="bg-card rounded-lg border p-6">
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-6"
              >
                <FormField
                  control={form.control}
                  name="position"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-base font-semibold">
                        Chess Position
                      </FormLabel>
                      <FormControl>
                        <Textarea
                          {...field}
                          label=""
                          placeholder="Enter a chess position in FEN notation (e.g., 'rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1') or describe the position in plain English (e.g., 'White has a queen and king vs black king and rook on the 7th rank')"
                          minRows={4}
                          maxRows={8}
                          maxLength={2000}
                          showCount
                          className="resize-none"
                        />
                      </FormControl>
                      <FormDescription>
                        You can use FEN notation for precise positions or
                        describe the position in natural language.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Button
                  type="submit"
                  disabled={isLoading || !form.formState.isValid}
                  className="w-full sm:w-auto"
                  size="lg"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Analyzing Position...
                    </>
                  ) : (
                    <>
                      <Brain className="mr-2 h-4 w-4" />
                      Evaluate Position
                      <ChevronRight className="ml-2 h-4 w-4" />
                    </>
                  )}
                </Button>
              </form>
            </Form>
          </div>

          {/* Results Section */}
          {evaluation && (
            <div className="bg-card rounded-lg border p-6">
              <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
                <Brain className="h-5 w-5 text-primary" />
                AI Evaluation
              </h2>
              <div className="prose prose-sm max-w-none dark:prose-invert">
                <div
                  className="whitespace-pre-wrap text-sm leading-relaxed"
                  dangerouslySetInnerHTML={{
                    __html: evaluation
                      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                      .replace(/\*(.*?)\*/g, '<em>$1</em>')
                      .replace(/^(\d+\.\s.*?)$/gm, '<strong>$1</strong>')
                      .replace(/\n/g, '<br>'),
                  }}
                />
              </div>
            </div>
          )}

          {/* Help Section */}
          <div className="bg-muted/50 rounded-lg p-6">
            <h3 className="font-semibold mb-3">How to use:</h3>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li>
                • <strong>FEN Notation:</strong> Use standard FEN for precise
                position analysis
              </li>
              <li>
                • <strong>Natural Language:</strong> Describe pieces and their
                positions in plain English
              </li>
              <li>
                • <strong>Examples:</strong> "White king on e1, black king on
                e8, white queen on d1" or "Sicilian Defense after 1.e4 c5 2.Nf3"
              </li>
              <li>
                • <strong>Analysis:</strong> Get comprehensive evaluation
                including tactics, strategy, and move recommendations
              </li>
            </ul>
          </div>
        </div>
      </main>
    </>
  );
};

export default EvaluateChessPositionPage;
