import { NextRequest, NextResponse } from 'next/server';
import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export async function POST(request: NextRequest) {
  try {
    const { position } = await request.json();

    if (!position || typeof position !== 'string') {
      return NextResponse.json(
        { error: 'Position is required and must be a string' },
        { status: 400 }
      );
    }

    if (!process.env.OPENAI_API_KEY) {
      return NextResponse.json(
        { error: 'OpenAI API key not configured' },
        { status: 500 }
      );
    }

    const completion = await openai.chat.completions.create({
      model: 'gpt-4',
      messages: [
        {
          role: 'system',
          content: `You are a chess grandmaster and expert chess position evaluator. Analyze the given chess position and provide a comprehensive evaluation. Your response should include:

1. **Overall Assessment**: Who is better and by how much (slight advantage, significant advantage, winning, etc.)
2. **Key Features**: Important pieces, pawn structure, king safety, piece activity
3. **Tactical Opportunities**: Any immediate tactics, threats, or combinations
4. **Strategic Considerations**: Long-term plans, weaknesses to exploit
5. **Recommended Moves**: 2-3 best candidate moves with brief explanations

Format your response in clear sections with markdown formatting for readability.`
        },
        {
          role: 'user',
          content: `Please evaluate this chess position: ${position}`
        }
      ],
      max_tokens: 1000,
      temperature: 0.7,
    });

    const evaluation = completion.choices[0]?.message?.content;

    if (!evaluation) {
      return NextResponse.json(
        { error: 'Failed to get evaluation from OpenAI' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      evaluation,
      usage: completion.usage,
    });

  } catch (error) {
    console.error('Error evaluating position:', error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: `Failed to evaluate position: ${error.message}` },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
